<template>
    <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />
    <MoreDetails :toggleDetails="toggleDetails" :showDetails="showDetails" />
    <UpgradeRequest :showUpgrade="showUpgrade" :toggleUpgrade="toggleUpgrade" />
    <ScreenerQst v-if="showScreener" :toggleScreener="toggleScreener" />
    <CreateAssesment v-if="showCreateAssesment" :toggleCreateAssesment="toggleCreateAssesment" />

    <div class="flex items-center">
        <div class="newAssessWrapper pb-10">
            <div class="w-full flex justify-between">
                <button class="backBtn text-gray-700 hover:bg-gray-50 hover:text-NeonBlue flex items-center py-2 px-3 font-md" @click="previousStep">
                    <font-awesome-icon :icon="['fas', 'angle-left']" class="mr-2" />
                    {{ $t("Previous") }}
                </button>
                <div class="mx-4 flex flex-col text-center">
                    <h2 class="projData">
                        {{ project.name ? project.name : "Untitled" }}
                        {{ project.seniority && " - " + project.seniority }}
                        {{ project.jobTitle && " - " + project.jobTitle }}
                    </h2>
                    <div class="flex justify-center" style="color: #2196f3">
                        <div class="flex items-center">
                            <font-awesome-icon :icon="['far', 'file-lines']" class="mx-2" />
                            <p>
                                {{ placeholders.filter((assessment) => assessment.assessment !== null).length }}
                                {{ $t("tests") }}
                            </p>
                        </div>
                        <div class="flex mx-2 items-center">
                            <font-awesome-icon :icon="['far', 'clock']" class="mx-2" />
                            <p>{{ totalDration }} {{ $t("minutes") }}</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <button class="nextStep" @click="nextStep" v-if="this.step < 4">
                        {{ $t("Next step") }}
                        <font-awesome-icon :icon="['fas', 'angle-right']" class="ml-2" />
                    </button>
                    <button class="nextStep" @click="PostJob" v-else-if="!this.isLoading">{{ $t("Finish") }}</button>
                    <button class="nextStep shadow flex items-center justify-center" v-else-if="this.isLoading">
                        <LoaderComponent />
                    </button>
                </div>
            </div>
            <div class="flex flex-row items-center justify-between relative my-14 mx-8">
                <span
                    v-for="(step, index) in steps"
                    :key="index"
                    class="relative text-white w-10 h-10 rounded-full flex justify-center items-center before:absolute before:bg-transparent before:border-2 before:border-white before:w-8 before:h-8 before:left-1/2 before:top-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full"
                    :class="`after:absolute after:-bottom-8 after:left-1/2 after:-translate-x-1/2 after:text-slate-700 after:content-['${step.title}'] ${
                        this.step >= step.number ? 'bg-CustomBlue' : 'bg-[#A7C6E3]'
                    } `"
                    @click="jumpTostep(step.number)"
                    >{{ step.number < this.step ? "&#10004;" : step.number }}
                    <h1
                        class="absolute -bottom-8 left-1/2 -translate-x-1/2 text-sm font-bold w-fit whitespace-normal overflow-visible text-center"
                        :class="this.step >= step.number ? 'text-CustomBlue' : 'text-[#A7C7E3]'"
                    >
                        {{ $t(step.title) }}
                    </h1>
                </span>
                <span
                    v-for="(step, index) in steps"
                    :key="index"
                    class="absolute h-0.5 top-1/2 -translate-y-1/2"
                    :class="index == steps.length - 1 ? 'hidden' : '' + ' ' + this.step > step.number ? 'bg-CustomBlue' : 'bg-[#A7C6E3]'"
                    :style="{ width: `${lineWidth()}%`, left: `${(lineWidth() + 8) * index + 7}%` }"
                ></span>
            </div>

            <div class="w-full">
                <div class="bg-[#fff] rounded-md" v-show="step === 1">
                    <form action="POST" @submit.prevent="onSubmit" ref="form1" class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto pt-10 px-12">
                        <!-- ... your form content for section 1 ... -->
                        <div class="input_group">
                            <Popper :content="$t('project name tooltip')" placement="right" :hover="true">
                                <font-awesome-icon class="text-base text-gray-400" :icon="['far', 'circle-question']" />
                            </Popper>
                            <input
                                type="text"
                                id="project_name"
                                name="project_name"
                                v-model="project.name"
                                @focus="isInputFocused = true"
                                :placeholder="$t('Project name')"
                                required
                                autocomplete="off"
                            />
                            <font-awesome-icon :icon="['fas', 'file-signature']" class="input-icon" />

                            <span v-if="requiredFields.project_name" class="err_msg">{{ requiredFields.project_name }} </span>
                        </div>

                        <div class="input_group">
                            <div class="fixed top-0 left-0 w-full h-full" v-if="showJobCat" @click="toggleDropdownCat"></div>
                            <Popper :content="$t('category tooltip')" placement="top" :hover="true">
                                <font-awesome-icon class="text-base text-gray-400" :icon="['far', 'circle-question']" />
                            </Popper>
                            <div class="custom-select w-full bg-white" @click="toggleDropdownCat">
                                <div class="w-full flex justify-between items-center">
                                    <input
                                        type="text"
                                        id="jobRole"
                                        @click.stop="toggleDropdownCat"
                                        @click="toggleDropdownCat"
                                        autocomplete="off"
                                        name="jobRole"
                                        v-model="searchJobCat"
                                        @focus="
                                            () => {
                                                toggleDropdownCat();
                                                isInputFocused = true;
                                            }
                                        "
                                        :placeholder="$t('Job Category')"
                                        required
                                    />
                                    <font-awesome-icon :icon="['fas', 'sitemap']" class="absolute left-3 top-1/2 -translate-y-1/2 text-[#2196f3]" />

                                    <font-awesome-icon :icon="['fas', 'chevron-down']" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400" />
                                </div>
                                <ul class="options" v-if="showJobCat">
                                    <li
                                        v-for="(jobPos, index) in categories"
                                        :key="index"
                                        @click.stop
                                        @click="
                                            () => {
                                                toggleDropdownCat();
                                                selectJobCat(jobPos);
                                            }
                                        "
                                    >
                                        {{ $t(jobPos) }}
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="input_group">
                            <Popper class="w-[10%]" :content="$t('job position tooltip')" placement="right" :hover="true">
                                <font-awesome-icon class="text-base text-gray-400" :icon="['far', 'circle-question']" />
                            </Popper>
                            <div class="fixed top-0 left-0 w-full h-full" v-if="showJobTitles" @click="toggleDropdown"></div>
                            <div class="custom-select w-full bg-white" @click="toggleDropdown">
                                <div class="w-full flex justify-between items-center">
                                    <input
                                        type="text"
                                        id="jobRole"
                                        @click.stop="toggleDropdown"
                                        @click="toggleDropdown"
                                        name="jobRole"
                                        v-model="searchJobRole"
                                        autocomplete="off"
                                        @focus="
                                            () => {
                                                toggleDropdown();
                                                isInputFocused = true;
                                            }
                                        "
                                        :placeholder="$t('Job Role')"
                                        required
                                    />
                                    <font-awesome-icon :icon="['fas', 'user-tie']" class="absolute left-3 top-1/2 -translate-y-1/2 text-[#2196f3]" />

                                    <font-awesome-icon :icon="['fas', 'chevron-down']" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400" />
                                </div>
                                <ul class="options" v-if="showJobTitles">
                                    <li
                                        v-for="(jobPos, index) in searchedJob"
                                        :key="index"
                                        @click.stop
                                        @click="
                                            () => {
                                                toggleDropdown();
                                                selectJobPosition(jobPos);
                                            }
                                        "
                                    >
                                        {{ jobPos.title }}
                                    </li>
                                </ul>
                            </div>
                            <span v-if="requiredFields.job_title" class="err_msg">{{ requiredFields.job_title }} </span>
                        </div>
                        <div class="input_group select">
                            <Popper :content="$t('job seniority tooltip')" placement="bottom" :hover="true">
                                <font-awesome-icon class="text-base text-gray-400" :icon="['far', 'circle-question']" />
                            </Popper>
                            <div class="fixed top-0 left-0 w-full h-full" v-if="showSeniority" @click="toggleSeniority"></div>
                            <div class="custom-select w-full bg-white" @click="toggleSeniority">
                                <div class="w-full h-[3rem] flex justify-between items-center">
                                    <input
                                        type="text"
                                        id="jobRole"
                                        @click.stop="toggleSeniority"
                                        @click="toggleSeniority"
                                        autocomplete="off"
                                        name="jobRole"
                                        v-model="project.seniority"
                                        @focus="
                                            () => {
                                                toggleSeniority();
                                                // isInputFocused = true;
                                            }
                                        "
                                        :placeholder="$t('Job Seniority')"
                                        required
                                    />

                                    <font-awesome-icon :icon="['fas', 'ranking-star']" class="absolute left-3 top-1/2 -translate-y-1/2 text-[#2196f3]" />

                                    <font-awesome-icon :icon="['fas', 'chevron-down']" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400" />
                                </div>

                                <ul class="options" v-if="showSeniority">
                                    <li
                                        v-for="(seniority, index) in seniorities"
                                        :key="index"
                                        @click.stop
                                        @click="
                                            () => {
                                                toggleSeniority();
                                                selectSeniority(seniority);
                                            }
                                        "
                                    >
                                        {{ $t(seniority) }}
                                    </li>
                                </ul>
                            </div>
                            <span v-if="requiredFields.job_seniority" class="err_msg">{{ requiredFields.job_seniority }}</span>
                        </div>
                    </form>
                </div>
                <div v-show="step === 2">
                    <div class="w-full mb-8 mt-20 flex items-center justify-end">
                        <button
                            @click="toggleCreateAssesment"
                            class="px-4 py-3 text-gray-900 border border-gray300 shadow rounded-md font-semibold hover:text-CustomBlue hover:border-CustomBlue transition-colors duration-200"
                        >
                            {{ $t("Build your Assessment") }}
                        </button>
                    </div>

                    <div v-if="loadingAssess" class="w-full h-[300px] flex justify-center items-center">
                        <NewAssessLoader />
                    </div>
                    <div v-else>
                        <div class="myTests">
                            <EmptyTest
                                v-for="(placeholder, index) in placeholders"
                                :key="index"
                                :index="index"
                                :assessment="placeholder.assessment"
                                :handleDrop="handleDrop"
                                :handleDragOver="handleDragOver"
                                :removeAssessment="removeAssessment"
                            />
                        </div>

                        <div class="my-5">
                            <NewAssessLib
                                @programming-skill-selected="handleProgrammingSkillSelected"
                                :removeAssessment="removeAssessment"
                                :recommendations="this.recommendations"
                                :selectedOption="selectedOption"
                                :placeholders="placeholders"
                                :addNewAssessment="addAssessment"
                            />
                        </div>
                    </div>
                </div>
                <div v-show="step === 3">
                    <div class="w-full mb-8 mt-20 flex items-center justify-end">
                        <button
                            @click="toggleScreener"
                            class="px-4 py-3 text-gray-900 border border-gray300 shadow rounded-md font-semibold hover:text-CustomBlue hover:border-CustomBlue transition-colors duration-200"
                        >
                            {{ this.Store.screenersQst.length > 0 ? $t("Update Screeners") : $t("Add Screeners") }}
                        </button>
                    </div>
                    <h1 v-if="this.Store.screenersQst.length > 0" class="screenersTitle my-6">{{ $t("My Screeners") }}:</h1>
                    <div v-if="this.Store.screenersQst.length > 0" class="w-full flex flex-col justify-start items-center shadow-inner mb-10">
                        <div
                            class="font-bold flex flex-row justify-start items-center w-full text-slate-700 text-base text-start border-b border-slate-300 rounded-t bg-white shadow-[0_4px_6px_1px_rgba(0,0,0,10%)]"
                        >
                            <div class="h-12 p-4 w-1/12 flex items-center">{{ $t("TYPE") }}</div>
                            <!-- <div class="h-12 p-4 w-1/12 flex items-center">TIME</div> -->
                            <div class="h-12 p-4 w-10/12 flex items-center">{{ $t("QUESTION") }}</div>
                        </div>
                        <div
                            v-for="(qst, index) in this.Store.screenersQst"
                            :key="index"
                            class="group flex flex-col justify-start items-center w-full text-slate-700 text-base text-start border-b border-slate-300 bg-white"
                            :class="qst.showMore ? 'my-4 shadow-inner rounded-md' : 'hover:bg-[#A7C6E3]'"
                        >
                            <div class="relative flex flex-row justify-start items-center w-full h-12 text-sm font-light overflow-hidden">
                                <div class="p-4 w-1/12 text-xs">{{ qst.type }}</div>
                                <div class="p-4 w-96 whitespace-nowrap">{{ qst.question }}</div>
                            </div>
                            <div class="w-full flex flex-col justify-center items-start overflow-hidden px-10 h-0" :class="qst.showMore ? 'qstDown py-10' : ''">
                                <p class="w-1/2 my-4 text-base font-normal text-slate-700">{{ qst.description }}</p>
                                <p class="w-1/2 my-4 text-base font-normal text-slate-700">{{ qst?.tip }}</p>
                            </div>
                        </div>
                    </div>

                    <h1 class="screenersTitle my-6">{{ $t("Qustom Questions") }}</h1>
                    <div class="w-full flex flex-col items-center justify-between">
                        <div class="w-full text-base font-light text-slate-700 bg-[#beddfa] p-1 rounded mb-4 flex flex-row items-center justify-between">
                            <span class="h-full px-4">{{ $t("You can add up to 5 custom questions to your assessment from our library, or create your own") }}</span>
                            <button class="h-full p-3 px-4 bg-white rounded-sm" @click="defineType('Essay')">{{ $t("Create custom question") }}</button>
                        </div>

                        <div class="w-full flex flex-col justify-start items-center shadow-inner">
                            <div
                                class="font-bold flex flex-row justify-start items-center w-full text-slate-700 text-base text-start border-b border-slate-300 rounded-t bg-white shadow-[0_4px_6px_1px_rgba(0,0,0,10%)]"
                            >
                                <div class="h-12 p-2 w-1/12 flex items-center">{{ $t("TYPE") }}</div>
                                <div class="h-12 p-2 w-1/12 flex items-center">{{ $t("TIME") }}</div>
                                <div class="h-12 p-2 w-10/12 flex items-center">{{ $t("QUESTION") }}</div>
                            </div>
                            <div
                                v-for="(qst, index) in selectedQuestions"
                                :key="index"
                                class="group flex flex-col justify-start items-center w-full text-slate-700 text-base text-start border-b border-slate-300 bg-white"
                                :class="qst.showMore ? 'my-4 shadow-inner rounded-md' : 'hover:bg-[#A7C6E3]'"
                            >
                                <div class="relative flex flex-row justify-start items-center w-full h-12 text-sm font-light overflow-hidden">
                                    <div class="p-2 w-1/12 text-xs">{{ $t(qst.category) }}</div>
                                    <div class="p-2 w-1/12 text-xs"><font-awesome-icon :icon="['far', 'clock']" class="w-3 h-3 font-extralight" />{{ " " + qst.time }} mins</div>
                                    <div class="p-2 w-96 whitespace-nowrap">{{ $t(qst.question) }}</div>
                                    <div
                                        class="absolute top-0 right-0 h-full flex flex-row gap-3 justify-end items-center bg-white text-slate-500 shadow-[-20px_0_6px_1px_rgba(255,255,255,80%)] px-3"
                                        :class="qst.showMore ? '' : 'group-hover:bg-[#A7C6E3] group-hover:shadow-none'"
                                    >
                                        <button
                                            @click="moveQstUp(index)"
                                            class="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease"
                                            :class="qst.shoxMore ? 'opacity-100' : 'opactity-0'"
                                        >
                                            <font-awesome-icon :icon="['fas', 'angle-up']" />
                                        </button>
                                        <button
                                            @click="moveQstDown(index)"
                                            class="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease"
                                            :class="qst.shoxMore ? 'opacity-100' : 'opactity-0'"
                                        >
                                            <font-awesome-icon :icon="['fas', 'angle-down']" />
                                        </button>

                                        <button
                                            @click="deleteSelectedQst(index)"
                                            class="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease"
                                            :class="qst.showMore ? 'opacity-100' : 'opactity-0'"
                                        >
                                            <font-awesome-icon :icon="['far', 'trash-can']" />
                                        </button>
                                    </div>
                                </div>
                                <div class="w-full flex flex-col justify-center items-start overflow-hidden px-10 h-0" :class="qst.showMore ? 'qstDown py-10' : ''">
                                    <p class="w-1/2 my-4 text-base font-normal text-slate-700">{{ qst.description }}</p>
                                    <p class="w-1/2 my-4 text-base font-normal text-slate-700">{{ qst?.tip }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <AddCustomQuestion :isShown="isShown" :closePanel="closePanel" :type="question_type" :addQst="addQst" />

                    <div class="my-5">
                        <CustomQuestionLib :selectedQuestions="selectedQuestions" :addNewAssessment="addAssessment" :addQst="addQst" :deleteSelectedQst="deleteSelectedQst" />
                    </div>
                </div>
                <div v-show="step === 4">
                    <ConfirmNewAssessment
                        :moveQstUp="moveQstUp"
                        :moveQstDown="moveQstDown"
                        :deleteQst="deleteSelectedQst"
                        :customQst="selectedQuestions"
                        :moveTestUp="moveTestUp"
                        :moveDownObject="moveDownObject"
                        :removeAssessment="removeAssessment"
                        :assessments="placeholders"
                        :setScore="setScore"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import EmptyTest from "@/components/EmptyTest.vue";
import AddCustomQuestion from "@/components/dashboard/createAssessment/AddCustomQuestion.vue";
import NewAssessLib from "@/components/dashboard/createAssessment/NewAssessLib.vue";
import CustomQuestionLib from "@/components/dashboard/createAssessment/CustomQuestionLib2.vue";
import ConfirmNewAssessment from "@/components/dashboard/createAssessment/ConfirmNewAssessment.vue";
import ToastNotification from "@/components/ToastNotification";
import LoaderComponent from "@/components/LoaderComponent.vue";
import CreateAssesment from "@/components/dashboard/createAssessment/CreateAssesment.vue";
import ScreenerQst from "@/components/dashboard/createAssessment/ScreenerQst.vue";
import NewAssessLoader from "@/components/dashboard/createAssessment/NewAssessLoader";
import MoreDetails from "@/components/NewAssessment/MoreDetails.vue";
import UpgradeRequest from "@/components/NewAssessment/UpgradeRequest.vue";
import { useStore } from "@/store/index";
import axios from "axios";
import { BASE_URL } from "@/constants";
export default {
    name: "NewAssessement",
    components: {
        CreateAssesment,
        ScreenerQst,
        EmptyTest,
        AddCustomQuestion,
        NewAssessLib,
        ConfirmNewAssessment,
        CustomQuestionLib,
        ToastNotification,
        LoaderComponent,
        NewAssessLoader,
        MoreDetails,
        UpgradeRequest,
    },
    data() {
        return {
            codingChallenge: false,
            showScreener: false,
            showUpgrade: false,
            showDetails: false,
            types: [
                { name: "Essay", icon: "@/assets/essay.svg" },
                { name: "Multiple-choice", icon: "@/assets/multiple-choice.svg" },
            ],
            showCreateAssesment: false,
            isLoading: false,
            question_type: "",
            categories: [
                "All",
                "Information Technology",
                "Software Development",
                "Sales and Marketing",
                "Education and Training",
                "Finance and Accounting",
                "Engineering",
                "Customer Service and Support",
                "Human Resources",
                "Manufacturing and Production",
                "Management and Leadership",
            ],
            isShown: false,
            selectedQuestions: [],
            project_id: "",
            customAssessment: {
                company: "",
                name: "",
                category: "",
                questions_list: [],
            },
            step: 1,
            project: {
                name: "",
                jobTitle: "",
                seniority: "",
                min_score: 70,
                assessments: [],
                is_coding_required: false,
            },
            requiredFields: {
                project_name: "",
                job_title: "",
                job_seniority: "",
            },
            placeholders: [
                { id: 1, assessment: null },
                { id: 2, assessment: null },
                { id: 3, assessment: null },
                { id: 4, assessment: null },
                { id: 5, assessment: null },
            ],
            seniorities: ["Entry Level", "Junior", "Senior"],
            showJobTitles: false,
            showJobCat: false,
            jobPositions: [],
            searchedJob: [],
            recommendations: [],
            searchJobRole: "",
            searchJobCat: "",
            message: "",
            isVisible: false,
            bgc: "",
            loadingAssess: true,
            steps: [
                { number: 1, title: "Create project" },
                { number: 2, title: "Select tests" },
                { number: 3, title: "Add questions" },
                { number: 4, title: "Review & configure" },
            ],
            showSeniority: false,
            selectedOption: "custom",
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        handleProgrammingSkillSelected(data) {
            if (data == "true") this.codingChallenge = true; // Set codingChallenge to true
        },
        toggleScreener() {
            this.showScreener = !this.showScreener;
        },
        toggleUpgrade() {
            this.showUpgrade = !this.showUpgrade;
        },
        toggleDetails() {
            this.showDetails = !this.showDetails;
        },
        selectRadio(optionValue) {
            // When an option is clicked, update the selectedOption value
            this.selectedOption = optionValue;
        },
        toggleCreateAssesment() {
            this.showCreateAssesment = !this.showCreateAssesment;
        },
        setScore(score) {
            this.project.min_score = score;
        },
        toggleSeniority() {
            this.showSeniority = !this.showSeniority;
            this.showJobTitles = false;
            this.showJobCat = false;
        },
        selectSeniority(seniority) {
            this.project.seniority = seniority;
        },

        defineType(type) {
            this.question_type = type;
            this.isShown = true;
        },
        closePanel() {
            this.isShown = false;
        },
        addQst(qst) {
            this.selectedQuestions.push({ ...qst, showMore: false, added: true });
        },
        lineWidth() {
            return 100 / (this.steps.length - 1) - 10;
        },
        showMore(id) {
            this.selectedQuestions[id].showMore = !this.selectedQuestions[id].showMore;
        },
        deleteSelectedQst(id) {
            this.selectedQuestions.splice(id, 1);
        },
        moveQstUp(id) {
            if (id === 0) return;
            const prev = this.selectedQuestions[id - 1];
            this.selectedQuestions[id - 1] = this.selectedQuestions[id];
            this.selectedQuestions[id] = prev;
        },
        moveQstDown(id) {
            if (id === this.selectedQuestions.length - 1) return;
            const prev = this.selectedQuestions[id + 1];
            this.selectedQuestions[id + 1] = this.selectedQuestions[id];
            this.selectedQuestions[id] = prev;
        },
        toggleDropdown() {
            this.showJobTitles = !this.showJobTitles;
            this.showJobCat = false;
            this.showSeniority = false;
        },
        toggleDropdownCat() {
            this.showJobCat = !this.showJobCat;
            this.showSeniority = false;
            this.showJobTitles = false;
        },
        selectJobPosition(jobPos) {
            this.project.jobTitle = jobPos.title;
            this.searchJobRole = jobPos.title;
            this.recommendations = jobPos.recommended;
        },
        selectJobCat(jobPos) {
            //this.project.jobTitle = jobPos.title;
            this.searchJobCat = jobPos;
            this.loading = true;
            axios
                .get(`${BASE_URL}/jobsPages/jobs`, {
                    params: {
                        category: this.searchJobCat,
                    },
                })
                .then((res) => {
                    //alert(res.data);
                    this.searchedJob = res.data.jobPositions;
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        validateForm() {
            // Reset requiredFields object
            this.requiredFields = {};

            // Check for required fields
            if (!this.project.name) this.requiredFields.project_name = "Project name is required";
            if (!this.project.jobTitle) this.requiredFields.job_title = "Job title is required";
            if (!this.project.seniority) this.requiredFields.job_seniority = "Job seniority is required";
            // Check if all required fields are filled
            return Object.keys(this.requiredFields).length === 0;
        },
        nextStep() {
            if (this.step < 4) {
                if (this.step === 1) {
                    if (this.validateForm()) {
                        this.loadingAssess = true;
                        this.step++;
                        setTimeout(() => {
                            this.loadingAssess = false;
                        }, 3600);
                        axios
                            .get(`${BASE_URL}/jobPositions/getRecommended?title=${this.project.jobTitle}&essentials=${this.selectedOption}`, {
                                withCredentials: true,
                            })
                            .then((res) => {
                                this.recommendations = res.data.recommendedIds;
                                setTimeout(() => {
                                    this.loadingAssess = false;
                                }, 2500);
                            })
                            .catch((error) => {
                                console.log(error);
                                setTimeout(() => {
                                    this.loadingAssess = false;
                                }, 2500);
                            });
                    }
                } else if (this.step === 2) {
                    if (this.placeholders[0]?.assessment !== null) {
                        this.step++;

                        // ...existing code...
                        const programmingAssessments = this.project.assessments.filter((assessment) => assessment.category == "Programming Skills");
                        if (programmingAssessments.length > 1) {
                            this.codingChallenge = true;
                        }
                    } else {
                        this.codingChallenge = true; // Reset coding challenge if no assessment is selected
                        this.step++;
                    }
                } else if (this.step === 3) this.step++;
            }
            if (this.step === 4) {
                console.log();
            }
        },
        jumpTostep(step) {
            switch (step) {
                case 1:
                    this.step = 1;
                    break;
                case 2:
                    if (this.validateForm()) this.step = 2;
                    break;
                case 3:
                    if (this.validateForm() && this.placeholders[0]?.assessment !== null) this.step = 3;
                    break;
                case 4:
                    if (this.validateForm() && this.placeholders[0]?.assessment !== null) this.step = 4;
                    break;
                default:
                    break;
            }
        },
        previousStep() {
            if (this.step == 1) {
                this.$router.go(-1);
            }
            if (this.step > 1) {
                this.step--;
            }
        },
        handleDragOver(index, event) {
            // Prevent default behavior to allow drop
            event.preventDefault();

            // Get the target element
            const targetElement = event.currentTarget;

            // Get the position of the mouse pointer relative to the target element
            const rect = targetElement.getBoundingClientRect();
            const offsetX = event.clientX - rect.left;
            // const offsetY = event.clientY - rect.top;

            // If the mouse pointer is more than 50% over the target element,
            // swap the cards
            if (offsetX > rect.width / 2) {
                // Swap the assessments in the array
                const draggedIndex = event.dataTransfer.getData("text/plain");
                const draggedAssessment = this.placeholders[draggedIndex];

                // Swap the assessments if the target index is not the same as the dragged index
                if (index !== draggedIndex) {
                    this.placeholders[draggedIndex] = this.placeholders[index];
                    this.placeholders[index] = draggedAssessment;
                }
            }
        },
        handleDrop(index, event) {
            // Prevent default behavior to allow drop
            event.preventDefault();

            // Get the index of the dragged item from the drag data
            const draggedIndex = event.dataTransfer.getData("text/plain");
            // Swap the assessments in the array
            const draggedAssessment = this.placeholders[draggedIndex];
            this.placeholders[draggedIndex] = this.placeholders[index];
            this.placeholders[index] = draggedAssessment;
        },
        addAssessment(assessment) {
            if (assessment.label == "premium" && !this.Store.premium && assessment.rating !== "essential") {
                this.message = "You need to upgrade to premium to add this test!";
                this.isVisible = true;
                this.bgc = "red";
                setTimeout(() => {
                    this.isVisible = false;
                }, 5000);
                return;
            }

            const emptyPlaceholder = this.placeholders.find((placeholder) => placeholder?.assessment === null);
            if (emptyPlaceholder) {
                emptyPlaceholder.assessment = assessment;

                // Calculate time attribute based on the logic provided
                const time = assessment.questions_nbr > 25 ? parseInt((20 * 35) / 60) : parseInt((assessment.questions_nbr * 35) / 60);
                emptyPlaceholder.assessment.time = time;
            } else {
                this.message = "You can add up to 5 tests only!";
                this.isVisible = true;
                this.bgc = "red";
                setTimeout(() => {
                    this.isVisible = false;
                }, 5000);
            }
        },
        removeAssessment(assessment) {
            const index = this.placeholders.findIndex((placeholder) => placeholder?.assessment === assessment);
            if (index !== -1) {
                // Set the assessment at the current index to null
                this.placeholders[index].assessment = null;

                // Shift assessments after the removed one to the left
                for (let i = index + 1; i < this.placeholders.length; i++) {
                    // Move the assessment one position to the left
                    this.placeholders[i - 1].assessment = this.placeholders[i]?.assessment;
                    // Clear the current assessment's position
                    this.placeholders[i].assessment = null;
                }
            }
        },
        moveTestUp(test) {
            const index = this.placeholders.findIndex((placeholder) => placeholder?.assessment === test);

            if (index > 0 && index < this.placeholders.length) {
                // Swap the object with the one above it
                [this.placeholders[index - 1], this.placeholders[index]] = [this.placeholders[index], this.placeholders[index - 1]];
            }
        },
        moveDownObject(test) {
            const index = this.placeholders.findIndex((placeholder) => placeholder?.assessment === test);

            // Check if the index is valid and not the last element
            if (index >= 0 && index < this.placeholders.length - 1) {
                // Swap the object with the one below it
                [this.placeholders[index], this.placeholders[index + 1]] = [this.placeholders[index + 1], this.placeholders[index]];
            }
        },
        async createCustomQuestionsTest() {
            const timestamp = Date.now();
            this.customAssessment.name = `(${this.project.name}) Custom Assessment #${timestamp}`;
            this.customAssessment.company = this.Store.company_name;
            this.customAssessment.category = "Custom";

            this.customAssessment.questions_list = [];
            this.selectedQuestions.map((question, index) => {
                if (question.name !== null) {
                    const newQuestion = {
                        question_number: index + 1,
                        time: question.time,
                        category: question.category,
                        title: question.name,
                        question: question.question,
                        options: question.options || {},
                    };
                    this.customAssessment.questions_list.push(newQuestion);
                }
            });

            return axios
                .post(`${BASE_URL}/uploadAssessment`, this.customAssessment, {
                    withCredentials: true,
                })
                .then((res) => {
                    this.project.assessments.push(res.data.newAs);

                    // Return the project to ensure that the calling function knows when it's done.
                    return this.project;
                })
                .catch((error) => {
                    console.log("Error trying to create custom assessment", error);
                    throw error; // Re-throw error to propagate it to the caller
                });
        },
        async setDuration(id, duration) {
            axios
                .put(`${BASE_URL}/uploadAssessment/duration/${id}`, { duration }, { withCredentials: true })
                .then(() => console.log("Duration updated succefusslly !!!!!!!!!"))
                .catch((error) => console.log("Error", error));
        },

        async updateProject() {
            this.project.assessments = [];
            this.placeholders.map(async (assessment) => {
                if (assessment.assessment !== null) {
                    this.project.assessments.push(assessment.assessment);
                    if (!assessment.assessment.duration) {
                        await this.setDuration(assessment.assessment._id, assessment.assessment.time);
                    }
                }
            });
            if (this.selectedQuestions.length > 0 && !this.customAssessment) {
                await this.createCustomQuestionsTest();
            }

            this.customAssessment.questions_list = this.selectedQuestions;
            this.project.assessments.push(this.customAssessment);
            this.project.screeners = this.Store.screenersQst;
            this.project.is_coding_required = this.codingChallenge;

            let data = JSON.stringify({
                project: this.project,
                customAssessment: this.customAssessment,
            });
            let config = {
                method: "put",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/projects/update`,
                headers: {
                    "Content-Type": "application/json",
                },
                data: data,
                withCredentials: true,
            };

            axios
                .request(config)
                .then(() => {
                    const locale = this.$route.params.locale || "en";

                    this.$router.push({
                        path: `/${locale}/boards`,
                        query: { id: this.project_id },
                    });
                })

                .catch((error) => {
                    console.log(error);
                    alert("there was an arror");
                });
        },
        async PostJob() {
            if (this.project_id) {
                this.updateProject();
                return;
            }
            this.isLoading = true;

            this.project.assessments = [];
            this.placeholders.map(async (assessment) => {
                if (assessment.assessment !== null) {
                    this.project.assessments.push(assessment.assessment);
                    if (!assessment.assessment.duration) {
                        await this.setDuration(assessment.assessment._id, assessment.assessment.time);
                    }
                }
            });
            this.project.screeners = this.Store.screenersQst;
            this.project.is_coding_required = this.codingChallenge;

            try {
                if (this.selectedQuestions.length > 0) {
                    await this.createCustomQuestionsTest();
                }
                let data = JSON.stringify({
                    project: this.project,
                });

                let config = {
                    method: "post",
                    maxBodyLength: Infinity,
                    url: `${BASE_URL}/projects/post`,
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: data,
                    withCredentials: true,
                };

                axios
                    .request(config)
                    .then((response) => {
                        const locale = this.$route.params.locale || "en";

                        this.$router.push({
                            path: `/${locale}/boards`,
                            query: {
                                id: response.data._id,
                            },
                            state: {
                                show: true,
                            },
                        });

                        this.project = {
                            name: "",
                            jobTitle: "",
                            seniority: "",
                            assessments: [],
                        };
                        this.isLoading = false;
                    })
                    .catch((error) => {
                        console.log({ error });
                        this.isLoading = false;
                    });
            } catch (error) {
                console.error(error);
                this.isLoading = false;
            }
        },
        async fetchProjectAssessments(id) {
            if (id) {
                let config = {
                    method: "get",
                    maxBodyLength: Infinity,
                    url: `${BASE_URL}/projects/project`,
                    headers: {
                        "Content-Type": "application/json",
                    },
                    params: {
                        id: id,
                    },
                    withCredentials: true,
                };

                await axios
                    .request(config)
                    .then((response) => {
                        this.project = response.data.project;
                        this.customAssessment =
                            response.data.project.assessments.find((assessment) => {
                                return assessment.category == "Custom";
                            }) || {};
                        this.Store.screenersQst = this.project.screeners;

                        this.selectedQuestions = this.customAssessment.questions_list ? JSON.parse(JSON.stringify(this.customAssessment.questions_list)) : [];
                        this.project.assessments = response.data.project.assessments.filter((assessment) => {
                            return assessment.category != "Custom";
                        });
                        this.searchJobRole = this.project.jobTitle;
                        this.project.assessments.map((assessment, index) => {
                            this.placeholders[index].assessment = assessment;
                        });
                        this.score = response.data.score;
                    })
                    .catch((error) => {
                        console.log(error);
                    });
            }
        },
    },
    watch: {
        async project_id(newVal) {
            await this.fetchProjectAssessments(newVal);
        },
        placeholders: {
            immediate: true, // Trigger the watcher immediately when the component is created
        },
        selectedQuestions: {
            handler(newVal) {
                // if (newVal.length > 0) {
                console.log({ ASSESSMENTSSSSSFROMTHEWATCHER: newVal });
                // }
            },
        },

        searchJobRole: {
            handler(newVal) {
                // if (newVal.length > 0) {
                this.searchedJob = this.jobPositions.filter((job) => job.title.toLowerCase().includes(newVal.toLowerCase()));
                // }
            },
        },
    },
    computed: {
        totalDration: {
            get() {
                return this.placeholders.reduce((acc, placeholder) => {
                    if (placeholder?.assessment !== null) {
                        if (placeholder?.assessment?.questions_nbr > 25) {
                            return acc + parseInt((20 * 35) / 60);
                        } else {
                            return acc + parseInt((placeholder?.assessment?.questions_nbr * 35) / 60);
                        }
                    }
                    return acc;
                }, 0);
            },
        },
    },
    mounted() {
        this.project_id = this.$route.query.id;
        let config = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/jobPositions/all`,
            headers: {},
            withCredentials: true,
        };

        axios
            .request(config)
            .then((response) => {
                this.jobPositions = response.data.data;
                this.searchedJob = response.data.data;
            })
            .catch((error) => {
                console.log(error);
            });
    },
};
</script>

<style scoped lang="scss">
:deep(.popper) {
    background: #2371b6;
    padding: 10px;
    border-radius: 10px;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    max-width: 300px;
}

.newAssessWrapper {
    width: 90%;
    padding: 3% 5%;
    margin: 0 auto;
    margin-top: 1%;
    margin-bottom: 3%;
    background-color: #fff;
    height: max-content;
    border-radius: 6px;

    .backBtn {
        height: 50px;
        border-radius: 6px;
        border: 1px solid #d1d5db;
    }

    .projData {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 8px;
    }

    .nextStep {
        width: 120px;
        height: 50px;
        color: white;
        font-weight: 500;
        background: #2196f3;
        border-radius: 6px;

        &:hover {
            opacity: 0.8;
        }
    }

    .previousStep {
        width: 150px;
        height: 50px;
        background-color: #ededed;
        border: 1px solid #ededed;
        border-radius: 10px;
    }
}

.options {
    position: absolute;
    top: 110%;
    left: 0;
    height: fit-content;
    max-height: 32.5vh;
    overflow-y: scroll;
    width: 100%;
    margin: 0;
    padding: 5px 0;
    list-style: none;
    border-top: none;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #ccd7ec;
    box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);
    z-index: 1000 !important;
}

.options li {
    padding: 15px 20px;
    cursor: pointer;
    color: #05152e;
}

.options li:hover {
    background-color: #d5efff;
}

.input_group {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
    height: 3rem;

    .input-icon {
        position: absolute;
        left: 38px;
        top: 50%;
        transform: translateY(-50%);
        color: #2196f3;
        pointer-events: none;
    }

    input {
        border: 1px solid #ccd7ec;
        box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);
        border-radius: 8px;
        width: 100%;
        padding: 12px 16px 12px 40px;
        outline: none;
    }

    input {
        height: 3rem;
    }
}

.select {
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.myTests {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1.5rem;
}

.err_msg {
    color: #ff6969;
    font-size: 14px;
    font-weight: 300;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 125%;
    left: 0;
    padding: 0 1% 0 0;
    margin-left: 25px;
    transform: translateY(-50%);
    pointer-events: none;
}

.custom-select {
    position: relative;
    border-bottom: 1px solid #ccc;
    border-radius: 8px;
    overflow: visible;
    cursor: pointer;
    height: fit-content;
    border: 1px solid #ccd7ec;
    box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);

    input {
        border: none;
        box-shadow: none;
        width: 100%;
        height: 3rem;

        &:focus {
            border: none;
            outline: none;
        }
    }
}

.qstDown {
    height: fit-content;
    transition: all 700ms ease;
}

.w-fit {
    width: max-content;
}

.screenersTitle {
    color: #1b2559;
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    /* 160% */
    letter-spacing: -0.4px;
    width: 90%;
}
</style>
