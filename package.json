{"name": "go-platform", "version": "0.1.0", "private": true, "description": "## Project setup ``` npm install ```", "author": "", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "format": "prettier -w .", "format:check": "prettier -c .", "i18n:report": "vue-cli-service i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/locales/**/*.json\"", "lint:fix": "eslint src --fix && npm run format", "lint:strict": "eslint src", "prepare": "husky install"}, "main": "babel.config.js", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@google-cloud/local-auth": "^2.1.1", "@google/generative-ai": "^0.21.0", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.1.5", "@vue-hero-icons/solid": "^1.7.2", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueup/vue-quill": "^1.2.0", "axios": "1.8.3", "base64url": "^3.0.1", "class-variance-authority": "^0.7.0", "country-list": "^2.3.0", "d3": "^7.8.5", "exceljs": "^4.4.0", "google-auth-library": "^8.8.0", "googleapis": "^118.0.0", "gsap": "^3.12.5", "heroicons": "^2.1.5", "html2pdf.js": "^0.10.1", "intro.js": "^7.2.0", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "lodash": "^4.17.21", "marked": "^15.0.7", "morgan": "1.10.1", "papaparse": "^5.5.3", "pinia": "^2.1.6", "quill": "^2.0.2", "sheetjs": "^2.0.0", "slick-carousel": "^1.8.1", "socket.io": "^4.6.2", "socket.io-client": "^4.6.2", "swiper": "^8.4.7", "vue": "^3.4.31", "vue-analytics": "^5.22.1", "vue-awesome-swiper": "^5.0.1", "vue-cookies": "^1.8.6", "vue-gtag": "^2.0.1", "vue-i18n": "9.14.5", "vue-multiselect": "^3.0.0", "vue-performance": "^1.0.0", "vue-prism-component": "^2.0.0", "vue-quill-editor": "^3.0.6", "vue-recaptcha": "^2.0.3", "vue-router": "^4.3.3", "vue-simple-calendar": "^6.3.1", "vue-sweetalert2": "^5.0.11", "vue-swiper": "^0.5.0", "vue-yandex-metrika-v3": "^0.0.3", "vue3-carousel": "^0.3.3", "vue3-google-login": "^2.0.26", "vue3-highlightjs": "^1.0.5", "vue3-html2pdf": "^1.1.2", "vue3-marquee": "^4.1.0", "vue3-popper": "^1.5.0", "vue3-tel-input": "^1.0.4", "vue3-tour": "^0.3.4", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/eslint-parser": "^7.21.8", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.1.0", "@intlify/vue-i18n-loader": "^3.0.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-service": "~5.0.8", "autoprefixer": "^10.4.16", "compression-webpack-plugin": "^11.1.0", "critters-webpack-plugin": "^3.0.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.14.1", "husky": "^8.0.0", "image-webpack-loader": "^8.1.0", "lint-staged": "^15.0.2", "postcss": "^8.4.31", "postcss-import": "^16.1.0", "prettier": "^3.5.3", "sass": "^1.80.3", "sass-loader": "^16.0.2", "tailwindcss": "^3.3.5", "vue-cli-plugin-i18n": "~2.3.2", "vue-template-compiler": "^2.7.16", "vue-ua": "^1.7.0", "webpack": "^5.86.0", "webpack-bundle-analyzer": "^4.10.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && npx cz --hook || true"}}, "license": "ISC", "lint-staged": {"src/**/*.{js,vue,ts}": ["eslint", "prettier -w"], "src/**/*.{json,css,scss,md}": ["prettier -w"]}}