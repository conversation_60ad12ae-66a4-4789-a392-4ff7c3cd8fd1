<template>
    <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />
    <div class="fixed top-0 left-0 w-full h-full z-30 flex justify-center items-center bg-[#00000052] backdrop-blur-sm" v-if="showEmail">
        <div
            class="w-[90vw] max-w-[900px] h-[90vh] max-h-[700px] bg-white relative p-5 rounded-lg flex flex-col"
            style="
                box-shadow:
                    0 11px 15px -7px #0003,
                    0 24px 38px 3px #00000024,
                    0 9px 46px 8px #0000001f;
            "
        >
            <!-- Title and Close X in the same container -->
            <div class="flex items-center justify-between mb-6 flex-shrink-0">
                <span class="text-2xl font-bold text-[#1b2559]">{{ $t("Invite Candidates") }}</span>
                <button @click="closeInvitation" class="text-[#1b2559] text-4xl focus:outline-none ml-4" aria-label="Close">&times;</button>
            </div>
            <!-- Tabs Navigation -->
            <div class="flex border-b border-gray-300 mb-5">
                <button
                    :class="['py-3 px-5 font-medium flex items-center gap-2 transition duration-300', activeTab === 'links' ? 'border-b-2 border-NeonBlue text-NeonBlue' : 'text-[#1b2559]']"
                    @click="activeTab = 'links'"
                >
                    <font-awesome-icon :icon="['fas', 'link']" /> {{ $t("Share link") }}
                </button>

                <button
                    :class="['py-3 px-5 font-medium flex items-center gap-2 transition duration-300', activeTab === 'candidates' ? 'border-b-2 border-NeonBlue text-NeonBlue' : 'text-[#1b2559]']"
                    @click="activeTab = 'candidates'"
                >
                    <font-awesome-icon :icon="['fas', 'envelope-open-text']" /> {{ $t("Invite by email") }}
                </button>

                <button
                    :class="['py-3 px-5 font-medium flex items-center gap-2 transition duration-300', activeTab === 'bulk' ? 'border-b-2 border-NeonBlue text-NeonBlue' : 'text-[#1b2559]']"
                    @click="activeTab = 'bulk'"
                >
                    <font-awesome-icon :icon="['fas', 'users']" /> {{ $t("Invite in bulk") }}
                </button>

                <!-- New tab for viewing all invited candidates -->
                <button
                    :class="['py-3 px-5 font-medium flex items-center gap-2 transition duration-300', activeTab === 'invited' ? 'border-b-2 border-NeonBlue text-NeonBlue' : 'text-[#1b2559]']"
                    @click="activeTab = 'invited'"
                >
                    <font-awesome-icon :icon="['fas', 'list']" /> {{ $t("Invited Candidates") }}
                </button>
            </div>

            <!-- Tab Contents -->
            <div class="flex-1 min-h-0 overflow-y-auto">
                <div v-if="activeTab === 'links'" class="mt-4">
                    <!-- Invite Links Content -->
                    <div class="text-left px-6 mb-2">
                        <span class="text-base text-[#1b2559]">{{
                            $t("Invite candidates by sharing a public link in your job post. This link will direct them to the assessment and capture their name and email for tracking purposes.")
                        }}</span>
                    </div>

                    <!-- Invite Link Section -->
                    <div class="w-full mb-6">
                        <!-- Action Row: Only the right side content is used -->
                        <div class="flex flex-col gap-2 bg-white px-6 py-4" style="max-height: 320px; overflow-y: auto">
                            <div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
                                <font-awesome-icon :icon="['fas', 'link']" class="text-gray-500" />
                                <input type="text" :value="shortenedUrl || url" readonly class="flex-1 bg-transparent text-gray-700 outline-none truncate" />
                                <div class="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        id="shortenCheckbox"
                                        v-model="shouldShorten"
                                        @change="handleShortenToggle"
                                        :disabled="isShortening"
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                    <label for="shortenCheckbox" class="text-sm text-gray-700 whitespace-nowrap">
                                        {{ $t("Shorten URL") }}
                                    </label>
                                </div>
                                <button
                                    type="button"
                                    class="copy bg-NeonBlue text-white px-4 py-2 rounded-lg hover:opacity-85 transition flex items-center whitespace-nowrap"
                                    @click="copyLink"
                                    :disabled="isShortening"
                                >
                                    <font-awesome-icon :icon="['fas', 'copy']" class="mr-2" />
                                    <span class="copy-label">{{ $t("Copy link") }}</span>
                                </button>
                            </div>

                            <!-- Instruction Title -->
                            <div class="pt-2 text-sm font-semibold text-gray-700">{{ $t("Copy and add this text to your job post") }}</div>
                            <!-- Instruction Container -->
                            <div class="relative">
                                <div
                                    ref="instructionText"
                                    class="border border-gray-200 rounded-lg bg-gray-50 p-4 text-gray-700 text-sm whitespace-pre-line mt-1"
                                    v-html="$t('inviteCandidate.instructionText', { link: 'https://www.go-platform.com/', url: shortenedUrl || url })"
                                ></div>
                                <button @click="copyInstructionText" class="absolute bottom-2 right-2 text-gray-400 hover:text-NeonBlue transition" aria-label="Copy Instruction" title="Copy text">
                                    <font-awesome-icon :icon="['fas', 'copy']" class="text-lg" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="activeTab === 'candidates'" class="mt-4">
                    <!-- Invite Candidates Content -->
                    <div class="flex relative flex-col items-start justify-center px-6 gap-[0.5px]">
                        <span class="text-base text-[#1b2559]">{{ $t("Send an invitation email to candidates") }}</span>
                        <!-- Candidates Table -->
                        <div class="w-full my-5 h-fit max-h-[450px] overflow-x-auto overflow-y-auto">
                            <table class="w-full text-left">
                                <thead class="text-gray-700 text-sm font-semibold border-b border-gray-300">
                                    <tr>
                                        <th class="px-2 py-2">{{ $t("First Name") }}</th>
                                        <th class="px-2 py-2">{{ $t("Last Name") }}</th>
                                        <th class="px-2 py-2">{{ $t("Email") }}</th>
                                        <th class="px-2 py-2">{{ $t("Actions") }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white">
                                    <tr v-for="(candidate, index) in candidates" :key="index" class="border-[#dcdcdc] text-NeonBlue border-b bg-white border-neutral-200">
                                        <td class="whitespace-nowrap cursor-text transition duration-300 ease-in-out hover:bg-[#2371b631] px-2 py-4" @click="editCandidate(candidate, 'first_name')">
                                            <template v-if="!candidate.editing || candidate.editingField !== 'first_name'">{{ candidate.first_name }}</template>
                                            <input
                                                v-else
                                                class="border-[1px] focus:outline-none border-[#00000052] p-2 rounded-[8px]"
                                                type="text"
                                                v-model="candidate.first_name"
                                                @blur="saveCandidate(candidate)"
                                            />
                                        </td>
                                        <td class="whitespace-nowrap cursor-text transition duration-300 ease-in-out hover:bg-[#2371b631] px-2 py-4" @click="editCandidate(candidate, 'last_name')">
                                            <template v-if="!candidate.editing || candidate.editingField !== 'last_name'">{{ candidate.last_name }}</template>
                                            <input
                                                v-else
                                                class="border-[1px] focus:outline-none border-[#00000052] p-2 rounded-[8px]"
                                                type="text"
                                                v-model="candidate.last_name"
                                                @blur="saveCandidate(candidate)"
                                            />
                                        </td>
                                        <td class="whitespace-nowrap cursor-text transition duration-300 ease-in-out hover:bg-[#2371b631] px-2 py-4" @click="editCandidate(candidate, 'email')">
                                            <template v-if="!candidate.editing || candidate.editingField !== 'email'">{{ candidate.email }}</template>
                                            <input
                                                v-else
                                                class="border focus:outline-none border-[#00000052] p-2 rounded-[8px]"
                                                type="email"
                                                v-model="candidate.email"
                                                @blur="saveCandidate(candidate)"
                                            />
                                        </td>
                                        <td
                                            class="whitespace-nowrap text-[#DC3E42] transition duration-300 ease-in-out hover:bg-[#FFDBDC] px-2 py-5 cursor-pointer flex justify-center items-center"
                                            @click="deleteCandidate(candidate)"
                                        >
                                            <font-awesome-icon class="" :icon="['fas', 'xmark']" />
                                        </td>
                                    </tr>
                                    <tr class="">
                                        <td class="px-2 py-4 font-medium">
                                            <input
                                                class="border border-[#00000052] p-2 rounded-[8px] focus:outline-none focus:border-NeonBlue"
                                                type="text"
                                                :placeholder="$t('First Name')"
                                                @input="checkCandidate()"
                                                v-model="candidate.first_name"
                                            />
                                        </td>
                                        <td class="px-2 py-4">
                                            <input
                                                type="text"
                                                @input="checkCandidate()"
                                                :placeholder="$t('Last Name')"
                                                class="border border-[#00000052] p-2 rounded-[8px] focus:outline-none focus:border-NeonBlue"
                                                v-model="candidate.last_name"
                                            />
                                        </td>
                                        <td class="px-2 py-4 relative">
                                            <input
                                                type="email"
                                                @input="checkCandidate()"
                                                class="border border-[#00000052] p-2 rounded-[8px] focus:outline-none focus:border-NeonBlue"
                                                placeholder="Email"
                                                v-model="candidate.email"
                                            />
                                            <span class="text-red-500 text-xs absolute bottom-0 left-7">{{ emailError }}</span>
                                        </td>
                                        <td class="px-2 py-4">
                                            <button
                                                @click="addCandidate"
                                                class="w-auto transition duration-300 ease-in-out bg-white border border-NeonBlue text-NeonBlue flex rounded-md justify-center items-center px-3 py-2 hover:bg-[#e6f0ff]"
                                                :class="{ '': isValid, 'opacity-[0.8] cursor-not-allowed': !isValid }"
                                            >
                                                <font-awesome-icon :icon="['fas', 'user-plus']" class="mr-2 text-base" />
                                                <span class="text-base">{{ $t("Add") }}</span>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div v-if="mostInvitedCandidates[0]" class="flex ml-1 flex-col items-start">
                            <span class="text-[16px] mb-2 font-bold text-[#1b2559]">{{ $t("Most invited candidates") }} :</span>
                            <div class="w-full flex flex-col gap-0.5" v-for="(candidate, index) in mostInvitedCandidates" :key="index">
                                <div class="border-[#dcdcdc] flex items-center justify-between gap-1 text-[0.9rem] text-[#1b2559] border-neutral-200">
                                    <span class="whitespace-nowrap w-1/4 px-1 py-3">{{ candidate.first_name }} </span>
                                    <span class="whitespace-nowrap w-1/4 px-1 py-3">{{ candidate.last_name }} </span>
                                    <span class="whitespace-nowrap w-1/3 px-1 py-3">{{ candidate.email }}</span>
                                    <button
                                        @click="addMostInvitedCandidate(candidate)"
                                        class="w-1/4 transition duration-100 ease-in-out hover:underline hover:underline-offset-4 text-small text-[#2196f3] flex rounded justify-center items-center pl-40 py-4 px-9"
                                    >
                                        {{ $t("Add") }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full h-[80px] flex items-center" :class="{ 'justify-end': candidates.length === 0, 'justify-between': this.candidates.length > 0 }">
                        <div class="w-full flex justify-between items-center px-6">
                            <button v-if="this.candidates.length > 0" class="w-[20%] h-[40px] rounded-md font-medium bg-[#E5484D] hover:opacity-80 text-white" @click="clearList">
                                {{ $t("Clear List") }}
                            </button>
                            <div v-else class="w-[20%]"></div>

                            <div class="w-[40%] flex gap-2 justify-end items-center">
                                <ButtonComponent v-if="!isLoading" :action="sendInvitationEmail" intent="primary">
                                    {{
                                        this.candidates.length < 2
                                            ? $t("Send invitation")
                                            : `Invite
                                        ${this.candidates.length} Candidates`
                                    }}
                                </ButtonComponent>
                                <ButtonComponent v-else loading intent="primary">
                                    {{
                                        this.candidates.length < 2
                                            ? $t("Send invitation")
                                            : `Invite
                                                ${this.candidates.length} Candidates`
                                    }}
                                </ButtonComponent>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="activeTab === 'bulk'" class="mt-4">
                    <!-- Title -->
                    <div class="mb-6 px-6">
                        <span class="text-base text-[#1b2559]">{{ $t("Invite multiple candidates at once through a CSV or XLSX file upload.") }}</span>
                    </div>
                    <!-- Two containers side by side -->
                    <div class="flex flex-row gap-6 px-6">
                        <!-- Left Container: Example or Info -->
                        <div class="flex-1 border border-gray-200 rounded-lg bg-gray-50 p-6 flex flex-col items-start">
                            <span class="text-base font-semibold text-[#1b2559] mb-2">{{ $t("File Format Example") }}</span>
                            <table class="w-full text-left text-sm border border-gray-200 rounded">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-2 py-1 border border-gray-200">{{ $t("First Name") }}</th>
                                        <th class="px-2 py-1 border border-gray-200">{{ $t("Last Name") }}</th>
                                        <th class="px-2 py-1 border border-gray-200">{{ $t("Email") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="px-2 py-1 border border-gray-200">John</td>
                                        <td class="px-2 py-1 border border-gray-200">Doe</td>
                                        <td class="px-2 py-1 border border-gray-200"><EMAIL></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="mt-3 text-xs text-gray-500">{{ $t("Accepted formats:") }} <b>.csv</b>, <b>.xlsx</b></div>
                        </div>
                        <!-- Right Container: Instructions or Upload -->
                        <div class="flex-1 border border-gray-200 rounded-lg bg-gray-50 p-6 flex flex-col items-center">
                            <span class="text-base font-semibold text-[#1b2559] mb-2">{{ $t("Upload Candidates File") }}</span>
                            <label for="bulk-upload" class="shadow bg-NeonBlue text-white px-4 py-2 rounded-md cursor-pointer hover:opacity-85 transition inline-flex items-center gap-2">
                                <font-awesome-icon :icon="['fas', 'upload']" /> {{ $t("Upload CSV/XLSX") }}
                            </label>
                            <input id="bulk-upload" accept=".xlsx, .csv" type="file" @change="handleFileUpload($event)" class="hidden" />
                            <div v-if="uploadedFileName" class="mt-3 text-green-600 text-sm flex items-center gap-2">
                                <font-awesome-icon :icon="['fas', 'check-circle']" class="text-green-500" />
                                <span>{{ $t("File uploaded:") }} {{ uploadedFileName }}</span>
                                <button @click="removeUploadedFile" class="ml-2 flex items-center justify-center text-red-500 hover:text-red-700 p-0.5" title="Delete file">
                                    <font-awesome-icon :icon="['fas', 'trash-alt']" />
                                </button>
                            </div>
                            <div v-else class="mt-3 text-sm text-gray-600 text-center">
                                <p class="mb-2">{{ $t("Upload a file with candidate information") }}</p>
                                <p class="text-xs text-gray-500">
                                    {{ $t("Required columns:") }} <strong>First Name</strong>, <strong>Last Name</strong>, <strong>Email</strong>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">
                                    {{ $t("Maximum 1000 candidates per file") }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <!-- Send Invitation Button -->
                    <div class="w-full flex justify-end mt-8 items-center">
                        <div class="w-[40%] flex gap-2 justify-end items-center pr-6">
                            <ButtonComponent v-if="!isLoading" :action="sendInvitationEmail" intent="primary">
                                {{ $t("Send invitation") }}
                            </ButtonComponent>
                            <ButtonComponent v-else loading intent="primary">
                                {{ $t("Send invitation") }}
                            </ButtonComponent>
                        </div>
                    </div>
                </div>

                <!-- Invited Candidates Tab Content -->
                <div v-if="activeTab === 'invited'" class="mt-4">
                    <!-- Title and description -->
                    <div class="mb-6 px-6">
                        <span class="text-base text-[#1b2559]">{{ $t("View all candidates you have previously invited.") }}</span>
                    </div>

                    <!-- Loading State -->
                    <div v-if="loadingInvitedCandidates" class="flex justify-center items-center py-8">
                        <div class="flex items-center gap-2 text-NeonBlue">
                            <font-awesome-icon :icon="['fas', 'spinner']" class="fa-spin" />
                            <span>{{ $t("Loading invited candidates...") }}</span>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div v-else-if="invitedCandidatesError" class="flex justify-center items-center py-8">
                        <div class="text-red-500 text-center">
                            <font-awesome-icon :icon="['fas', 'exclamation-triangle']" class="mb-2" />
                            <p>{{ $t("Error loading invited candidates. Please try again.") }}</p>
                            <button @click="fetchInvitedCandidates" class="mt-2 px-4 py-2 bg-NeonBlue text-white rounded hover:opacity-85">
                                {{ $t("Retry") }}
                            </button>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-else-if="!loadingInvitedCandidates && invitedCandidatesList.length === 0" class="flex justify-center items-center py-8">
                        <div class="text-gray-500 text-center">
                            <font-awesome-icon :icon="['fas', 'inbox']" class="text-4xl mb-4 text-gray-300" />
                            <p class="text-lg">{{ $t("No invited candidates found") }}</p>
                            <p class="text-sm">{{ $t("Start inviting candidates using the other tabs!") }}</p>
                        </div>
                    </div>

                    <!-- Invited Candidates Table -->
                    <div v-else class="px-6">
                        <div class="w-full border border-gray-200">
                            <table class="w-full text-left bg-white">
                                <thead class="text-gray-700 text-sm font-semibold border-b border-gray-300 bg-gray-50 sticky top-0">
                                    <tr>
                                        <th class="px-4 py-3">{{ $t("First Name") }}</th>
                                        <th class="px-4 py-3">{{ $t("Last Name") }}</th>
                                        <th class="px-4 py-3">{{ $t("Email") }}</th>
                                        <th class="px-4 py-3">{{ $t("Assessment Status") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(candidate, index) in invitedCandidatesList"
                                        :key="candidate._id || index"
                                        class="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200"
                                    >
                                        <td class="px-4 py-3 text-gray-800">{{ candidate.first_name || "-" }}</td>
                                        <td class="px-4 py-3 text-gray-800">{{ candidate.last_name || "-" }}</td>
                                        <td class="px-4 py-3 text-gray-800">{{ candidate.email }}</td>
                                        <td class="px-4 py-3">
                                            <span
                                                v-if="candidate.assessmentStatus === 'not_started'"
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
                                            >
                                                <font-awesome-icon :icon="['fas', 'clock']" class="mr-1" />
                                                {{ $t("Not Started") }}
                                            </span>
                                            <span
                                                v-else-if="candidate.assessmentStatus === 'pending'"
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700"
                                            >
                                                <font-awesome-icon :icon="['fas', 'hourglass-half']" class="mr-1" />
                                                {{ $t("Submitted") }}
                                            </span>
                                            <span
                                                v-else-if="candidate.assessmentStatus === 'accepted'"
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700"
                                            >
                                                <font-awesome-icon :icon="['fas', 'check-circle']" class="mr-1" />
                                                {{ $t("Passed") }}
                                            </span>
                                            <span
                                                v-else-if="candidate.assessmentStatus === 'rejected'"
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700"
                                            >
                                                <font-awesome-icon :icon="['fas', 'times-circle']" class="mr-1" />
                                                {{ $t("Failed") }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Results Summary -->
                        <div class="mt-4 text-sm text-gray-600 text-center">{{ invitedCandidatesList.length }} {{ invitedCandidatesList.length === 1 ? $t("candidate") : $t("candidates") }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { BASE_URL } from "@/constants";
import ToastNotification from "@/components/ToastNotification";
import { useStore } from "@/store/index";

import ExcelJS from "exceljs";
import Papa from "papaparse";
import ButtonComponent from "@/components/ReusableComponents/ButtonComponent.vue";
export default {
    name: "InviteCandidate",
    props: ["showEmail", "toggleEmail", "projectId", "assessmentsLength"],
    components: { ToastNotification, ButtonComponent },
    data() {
        return {
            linkedinId: "YOUR_LINKEDIN_USER_ID", // Should be dynamically fetched
            title: "Your Title",
            text: "Your Description",
            url: "https://example.com",
            shortenedUrl: "", // Shortened URL
            isShortening: false, // Loading state for URL shortening
            shouldShorten: false, // Checkbox state
            imageUrl: "https://example.com/image.jpg",

            isAuthorized: false,
            email: "",
            thumbUrl: "www.google.com",
            activeTab: "links",
            isVisible: false,
            showInfo: false,
            message: "",
            bgc: "",
            accessToken: null,
            isLoading: false,
            candidates: [],
            mostInvitedCandidates: [],
            candidate: {
                first_name: "",
                last_name: "",
                email: "",
            },
            emailError: "",
            isValid: false,
            articleUrl: "",
            articleTitle: "",
            articleDescription: "",
            uploadedFileName: "",
            // New data properties for Invited Candidates tab
            invitedCandidatesList: [],
            loadingInvitedCandidates: false,
            invitedCandidatesError: false,
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },

    watch: {
        projectId: {
            async handler(newVal) {
                if (this.projectId) {
                    let config = {
                        method: "get",
                        maxBodyLength: Infinity,
                        url: `${BASE_URL}/inviteCandidate/multi-invite-link/` + newVal,
                        withCredentials: true,
                    };
                    axios
                        .request(config)
                        .then(async (response) => {
                            this.url = JSON.parse(JSON.stringify(response.data)).generatedLink;

                            // Only set href if the element exists
                            const dynamicLinkElement = document.getElementById("dynamicLink");
                            if (dynamicLinkElement) {
                                dynamicLinkElement.href = this.url;
                            }

                            // Reset shortened URL and checkbox when new URL is generated
                            this.shortenedUrl = "";
                            this.shouldShorten = false;
                        })
                        .catch((error) => {
                            console.log(error);
                        });
                }
            },
            immediate: true, // Trigger the watcher immediately on component creation
        },

        // Watch for activeTab changes to fetch invited candidates when needed
        activeTab: {
            handler(newTab) {
                if (newTab === "invited" && this.invitedCandidatesList.length === 0 && !this.loadingInvitedCandidates) {
                    // Fetch invited candidates when the tab is activated for the first time
                    this.fetchInvitedCandidates();
                }
            },
            immediate: false,
        },
    },

    methods: {
        // Handle checkbox toggle for URL shortening
        async handleShortenToggle() {
            if (this.shouldShorten && !this.shortenedUrl) {
                // User checked the box and we don't have a shortened URL yet
                await this.shortenUrl(this.url);
            } else if (!this.shouldShorten && this.shortenedUrl) {
                // User unchecked the box, reset to original URL
                this.resetToOriginalUrl();
            }
        },

        // Manual URL shortener - only runs when user checks the checkbox
        async shortenUrl(longUrl) {
            if (!longUrl) return;

            this.isShortening = true;
            try {
                const response = await axios.get(`https://tinyurl.com/api-create.php?url=${encodeURIComponent(longUrl)}`);
                this.shortenedUrl = response.data;
            } catch (error) {
                console.error("Error shortening URL:", error);
                this.shortenedUrl = "";
                this.shouldShorten = false; // Uncheck the box on error
                // Show error message to user
                this.message = "Failed to shorten URL. Please try again.";
                this.bgc = "red";
                this.isVisible = true;
                setTimeout(() => {
                    this.isVisible = false;
                }, 3000);
            } finally {
                this.isShortening = false;
            }
        },

        // Reset to original URL when user unchecks the box
        resetToOriginalUrl() {
            this.shortenedUrl = "";
            this.shouldShorten = false;
        },

        clearList() {
            this.candidates = [];
        },
        copyLink() {
            const urlToCopy = this.shortenedUrl || this.url;
            document.querySelectorAll(".copy").forEach((copyButton) => {
                // Function to handle the copy action
                const handleCopyEvent = () => {
                    navigator.clipboard.writeText(urlToCopy).then(() => {
                        const label = copyButton.querySelector(".copy-label");
                        const originalText = label.textContent;
                        copyButton.disabled = true;
                        label.textContent = "Copied!";

                        setTimeout(() => {
                            copyButton.disabled = false;
                            label.textContent = originalText;
                        }, 2000);
                    });
                };

                // Add event listeners for different types of clicking events
                const clickEvents = ["click", "mousedown", "mouseup", "dblclick", "contextmenu"];
                clickEvents.forEach((eventType) => {
                    copyButton.addEventListener(eventType, handleCopyEvent);
                });
            });
        },

        closeInvitation() {
            this.toggleEmail();
            this.clearList();
        },
        sendInvitationEmail() {
            if (this.assessmentsLength === 0) {
                this.message = "Please add an assessment to invite candidates";
                this.bgc = "red";
                this.isVisible = true;
                setTimeout(() => {
                    this.isVisible = false;
                }, 5000);
            }
            if (this.candidates.length > 0 && this.projectId) {
                if (this.candidates.length > this.Store.companyCredit && this.Store.companyPlan == "pay-per-use") {
                    this.message = "You do not have enough credit to invite this number of candidates";
                    this.bgc = "red";
                    this.isVisible = true;
                    setTimeout(() => {
                        this.isVisible = false;
                    }, 5000);
                } else {
                    this.isLoading = true;
                    let data = JSON.stringify({
                        candidates: this.candidates,
                        projectId: this.projectId,
                        company_name: this.Store.company_name,
                        // Include the shortened URL in the email if available
                        inviteUrl: this.shortenedUrl || this.url,
                    });

                    let config = {
                        method: "post",
                        maxBodyLength: Infinity,
                        url: `${BASE_URL}/inviteCandidate`,
                        headers: {
                            "Content-Type": "application/json",
                        },
                        withCredentials: true,
                        data: data,
                    };

                    axios
                        .request(config)
                        .then((response) => {
                            this.toggleEmail();
                            this.message = response.data.message;
                            this.isLoading = false;
                            this.bgc = "success";
                            this.candidates = [];
                            this.email = "";
                            this.isVisible = true;
                            this.Store.getCompanyCredit();
                            setTimeout(() => {
                                this.isVisible = false;
                            }, 5000);
                        })
                        .catch((error) => {
                            console.log({ error });
                            this.isLoading = false;
                            this.message = "Please enter a valid email address";
                            this.bgc = "red";
                            this.email = "";
                            this.isVisible = true;
                            setTimeout(() => {
                                this.isVisible = false;
                            }, 5000);
                        });
                }
            } else {
                alert("Please add at least one candidate to invite");
            }
        },
        editCandidate(candidate, field) {
            candidate.editing = true;
            candidate.editingField = field;
        },
        saveCandidate(candidate) {
            // Count the occurrences of the email address in the candidates array
            const emailOccurrences = this.candidates.filter((candid) => candidate.email === candid.email).length;

            if (emailOccurrences < 2) {
                // If there are less than two candidates with the same email, proceed with saving the candidate
                candidate.editing = false;
                candidate.editingField = null;
            } else {
                alert("Email already added for two candidates");
            }
        },
        deleteCandidate(candidate) {
            const index = this.candidates.indexOf(candidate);
            if (index !== -1) {
                this.candidates.splice(index, 1);
            } else {
                console.error("Candidate not found");
            }
        },

        checkCandidate() {
            let isValid = true;
            this.emailError = "";
            const emailRegex = /^\S+@\S+\.\S+$/; // Regular expression for basic email format

            if (!emailRegex.test(this.candidate.email) || this.candidate.email.includes(",")) {
                isValid = false; // Update the formValid flag
                this.emailError = "Please enter a valid email";
            }
            if (!this.candidate.first_name || !this.candidate.last_name) {
                isValid = false;
            }
            if (!this.candidate.first_name && !this.candidate.last_name && !this.candidate.email) {
                this.emailError = "";
            }
            this.isValid = isValid;
            return isValid;
        },
        addCandidate() {
            const duplicatedCandid = this.candidates.find((candid) => this.candidate.email === candid.email);
            if (this.checkCandidate() && !duplicatedCandid) {
                this.candidates.push(this.candidate);
                this.candidate = {
                    first_name: "",
                    last_name: "",
                    email: "",
                };
                this.isValid = false;
            }
        },
        addMostInvitedCandidate(candidate) {
            const duplicatedCandid = this.candidates.find((candid) => candidate.email === candid.email);
            if (!duplicatedCandid) {
                this.candidates.push(candidate);
            }
        },
        handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            const validationResult = this.validateFileType(file);
            if (!validationResult.isValid) {
                this.showErrorMessage(validationResult.message);
                this.clearFileInput();
                return;
            }

            this.uploadedFileName = file.name;
            const reader = new FileReader();

            reader.onload = (e) => {
                const data = e.target.result;

                if (file.type === "text/csv" || file.name.toLowerCase().endsWith('.csv')) {
                    this.processCsvFile(data, file);
                } else if (file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || file.name.toLowerCase().endsWith('.xlsx')) {
                    this.processXlsxFile(data, file);
                }
            };

            reader.onerror = () => {
                this.showErrorMessage("Failed to read the file. Please try again.");
                this.clearFileInput();
            };

            if (file.type === "text/csv" || file.name.toLowerCase().endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsArrayBuffer(file);
            }
        },

        validateFileType(file) {
            const allowedTypes = [
                'text/csv',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            const allowedExtensions = ['.csv', '.xlsx'];

            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            // Check file type and extension
            if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                return {
                    isValid: false,
                    message: "Invalid file format. Please upload a CSV (.csv) or Excel (.xlsx) file."
                };
            }

            // Check file size (limit to 10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                return {
                    isValid: false,
                    message: "File size too large. Please upload a file smaller than 10MB."
                };
            }

            return { isValid: true };
        },

        processCsvFile(data) {
            Papa.parse(data, {
                header: true,
                skipEmptyLines: true,
                complete: (result) => {
                    const validation = this.validateFileData(result.data, 'csv');
                    if (!validation.isValid) {
                        this.showErrorMessage(validation.message);
                        this.clearFileInput();
                        return;
                    }

                    const parsedCandidates = result.data
                        .filter(row => row.first_name || row.last_name || row.email) // Filter out empty rows
                        .map((row) => ({
                            first_name: (row.first_name || row['First Name'] || '').toString().trim(),
                            last_name: (row.last_name || row['Last Name'] || '').toString().trim(),
                            email: (row.email || row['Email'] || '').toString().trim(),
                        }));

                    const finalValidation = this.validateCandidateData(parsedCandidates);
                    if (!finalValidation.isValid) {
                        this.showErrorMessage(finalValidation.message);
                        this.clearFileInput();
                        return;
                    }

                    this.candidates.push(...parsedCandidates);
                    this.showSuccessMessage(`Successfully imported ${parsedCandidates.length} candidates from CSV file.`);
                },
                error: () => {
                    this.showErrorMessage("Failed to parse CSV file. Please check the file format and try again.");
                    this.clearFileInput();
                }
            });
        },

        processXlsxFile(data) {
            const workbook = new ExcelJS.Workbook();
            workbook.xlsx.load(data)
                .then(() => {
                    const worksheet = workbook.getWorksheet(1);
                    if (!worksheet) {
                        this.showErrorMessage("Excel file appears to be empty or corrupted.");
                        this.clearFileInput();
                        return;
                    }

                    const jsonData = [];
                    let headerRow = null;

                    worksheet.eachRow((row, rowNumber) => {
                        const rowValues = row.values.slice(1); // Skip undefined first element

                        if (rowNumber === 1) {
                            // Store header row for validation
                            headerRow = rowValues.map(val => val ? val.toString().toLowerCase().trim() : '');
                        } else if (rowValues.some(val => val && val.toString().trim())) {
                            // Only process non-empty rows
                            jsonData.push({
                                first_name: rowValues[0] ? rowValues[0].toString().trim() : '',
                                last_name: rowValues[1] ? rowValues[1].toString().trim() : '',
                                email: rowValues[2] ? rowValues[2].toString().trim() : '',
                            });
                        }
                    });

                    const validation = this.validateFileData(jsonData, 'xlsx', headerRow);
                    if (!validation.isValid) {
                        this.showErrorMessage(validation.message);
                        this.clearFileInput();
                        return;
                    }

                    const finalValidation = this.validateCandidateData(jsonData);
                    if (!finalValidation.isValid) {
                        this.showErrorMessage(finalValidation.message);
                        this.clearFileInput();
                        return;
                    }

                    this.candidates.push(...jsonData);
                    this.showSuccessMessage(`Successfully imported ${jsonData.length} candidates from Excel file.`);
                })
                .catch((error) => {
                    console.error("Excel parsing error:", error);
                    this.showErrorMessage("Failed to parse Excel file. Please ensure it's a valid .xlsx file and try again.");
                    this.clearFileInput();
                });
        },

        validateFileData(data, fileType, headerRow = null) {
            // Check if file has data
            if (!data || data.length === 0) {
                return {
                    isValid: false,
                    message: `The ${fileType.toUpperCase()} file appears to be empty. Please upload a file with candidate data.`
                };
            }

            // For Excel files, validate header structure
            if (fileType === 'xlsx' && headerRow) {
                const requiredHeaders = ['first_name', 'last_name', 'email'];
                const hasValidHeaders = requiredHeaders.some(header =>
                    headerRow.some(h => h.includes(header.replace('_', ' ')) || h.includes(header))
                );

                if (!hasValidHeaders) {
                    return {
                        isValid: false,
                        message: "Excel file must have headers: 'First Name', 'Last Name', and 'Email' in the first row."
                    };
                }
            }

            // Check if file has too many rows (limit to 1000)
            if (data.length > 1000) {
                return {
                    isValid: false,
                    message: "File contains too many rows. Maximum allowed is 1000 candidates per upload."
                };
            }

            return { isValid: true };
        },

        validateCandidateData(candidates) {
            const validationResults = this.analyzeValidationErrors(candidates);

            if (validationResults.errorCount > 0) {
                const errorMessage = this.buildValidationErrorMessage(validationResults);
                return {
                    isValid: false,
                    message: errorMessage
                };
            }

            return { isValid: true };
        },

        analyzeValidationErrors(candidates) {
            const emailRegex = /^\S+@\S+\.\S+$/;
            const seenEmails = new Set();
            const results = {
                errorCount: 0,
                missingFieldsCount: 0,
                invalidEmailsCount: 0,
                duplicateEmailsCount: 0,
                firstErrorRows: []
            };

            candidates.forEach((candidate, index) => {
                const rowNumber = index + 2;
                const rowErrors = this.validateSingleCandidate(candidate, seenEmails, emailRegex);

                if (rowErrors.hasError) {
                    results.errorCount++;
                    this.updateErrorCounts(results, rowErrors);
                    this.addErrorExample(results, rowNumber, rowErrors);
                }
            });

            return results;
        },

        validateSingleCandidate(candidate, seenEmails, emailRegex) {
            const result = {
                hasError: false,
                missingFields: [],
                invalidEmail: false,
                duplicateEmail: false
            };

            // Check required fields
            if (!candidate.first_name?.trim()) result.missingFields.push("First Name");
            if (!candidate.last_name?.trim()) result.missingFields.push("Last Name");
            if (!candidate.email?.trim()) result.missingFields.push("Email");

            // Check email format
            if (candidate.email?.trim() && !emailRegex.test(candidate.email.trim())) {
                result.invalidEmail = true;
            }

            // Check for duplicates
            if (candidate.email?.trim()) {
                const email = candidate.email.trim().toLowerCase();
                if (seenEmails.has(email)) {
                    result.duplicateEmail = true;
                } else {
                    seenEmails.add(email);
                }
            }

            result.hasError = result.missingFields.length > 0 || result.invalidEmail || result.duplicateEmail;
            return result;
        },

        updateErrorCounts(results, rowErrors) {
            if (rowErrors.missingFields.length > 0) results.missingFieldsCount++;
            if (rowErrors.invalidEmail) results.invalidEmailsCount++;
            if (rowErrors.duplicateEmail) results.duplicateEmailsCount++;
        },

        addErrorExample(results, rowNumber, rowErrors) {
            if (results.firstErrorRows.length >= 3) return;

            let errorDescription = '';
            if (rowErrors.missingFields.length > 0) {
                errorDescription = `Missing: ${rowErrors.missingFields.join(', ')}`;
            } else if (rowErrors.invalidEmail) {
                errorDescription = 'Invalid email';
            } else if (rowErrors.duplicateEmail) {
                errorDescription = 'Duplicate email';
            }

            if (errorDescription) {
                results.firstErrorRows.push(`Row ${rowNumber}: ${errorDescription}`);
            }
        },

        buildValidationErrorMessage(results) {
            const { firstErrorRows } = results;

            if (firstErrorRows.length === 0) {
                return "File contains invalid data. Please check your file format.";
            }

            // Show only the first error for simplicity
            const firstError = firstErrorRows[0];

            // Convert "Row X: Missing: First Name, Last Name" to "Missing first name and last name in row X"
            if (firstError.includes('Missing:')) {
                const rowMatch = firstError.match(/Row (\d+):/);
                const fieldsMatch = firstError.match(/Missing: (.+)/);
                if (rowMatch && fieldsMatch) {
                    const rowNum = rowMatch[1];
                    const fields = fieldsMatch[1].toLowerCase();
                    return `Missing ${fields} in row ${rowNum}`;
                }
            }

            // Convert "Row X: Invalid email format" to "Invalid email in row X"
            if (firstError.includes('Invalid email')) {
                const rowMatch = firstError.match(/Row (\d+):/);
                if (rowMatch) {
                    return `Invalid email in row ${rowMatch[1]}`;
                }
            }

            // Convert "Row X: Duplicate email" to "Duplicate email in row X"
            if (firstError.includes('Duplicate email')) {
                const rowMatch = firstError.match(/Row (\d+):/);
                if (rowMatch) {
                    return `Duplicate email in row ${rowMatch[1]}`;
                }
            }

            return firstError;
        },

        showErrorMessage(message) {
            this.message = message;
            this.bgc = "red";
            this.isVisible = true;
            setTimeout(() => {
                this.isVisible = false;
            }, 5000);
        },

        showSuccessMessage(message) {
            this.message = message;
            this.bgc = "success";
            this.isVisible = true;
            setTimeout(() => {
                this.isVisible = false;
            }, 3000);
        },

        clearFileInput() {
            this.uploadedFileName = "";
            const input = document.getElementById("bulk-upload");
            if (input) input.value = "";
        },

        copyInstructionText() {
            const el = this.$refs.instructionText;
            // Create a temporary element to get only the text content
            const temp = document.createElement("div");
            temp.innerHTML = el.innerHTML;
            // Remove all anchor tags but keep their text/href
            temp.querySelectorAll("a").forEach((a) => {
                a.replaceWith(a.href);
            });
            const text = temp.innerText;
            navigator.clipboard.writeText(text).then(() => {
                this.message = "Copied!";
                this.bgc = "success";
                this.isVisible = true;
                setTimeout(() => {
                    this.isVisible = false;
                }, 2000);
            });
        },
        removeUploadedFile() {
            this.uploadedFileName = "";
            // Optionally clear the file input as well
            const input = document.getElementById("bulk-upload");
            if (input) input.value = "";
        },

        // Fetch all invited candidates for the current user and specific project
        async fetchInvitedCandidates() {
            this.loadingInvitedCandidates = true;
            this.invitedCandidatesError = false;

            // Check if projectId is available
            if (!this.projectId) {
                console.warn("No project ID available for fetching invited candidates");
                this.invitedCandidatesError = true;
                this.loadingInvitedCandidates = false;
                return;
            }

            try {
                const config = {
                    method: "get",
                    maxBodyLength: Infinity,
                    url: `${BASE_URL}/invited-candidates/all?projectId=${this.projectId}`,
                    withCredentials: true,
                };

                console.log("Fetching invited candidates for project:", this.projectId);

                const response = await axios.request(config);

                this.invitedCandidatesList = response.data;
            } catch (error) {
                console.error("Error fetching invited candidates:", error);
                console.error("Error details:", error.response?.data);
                this.invitedCandidatesError = true;

                // Show error message to user
                this.message = "Failed to load invited candidates. Please try again.";
                this.bgc = "red";
                this.isVisible = true;
                setTimeout(() => {
                    this.isVisible = false;
                }, 3000);
            } finally {
                this.loadingInvitedCandidates = false;
            }
        },
    },
};
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />
<style lang="scss" scoped>
input::placeholder {
    font-weight: 300;
    /* You can adjust the value to a lighter weight as needed */
}

input {
    padding: 0.7rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #d8e2ee;
    font-weight: 200;
}

input[type="file"] {
    display: none;
}

::placeholder {
    font-family: "Roboto";
    font-weight: 700;
    font-size: 14px;
    color: #adb8cc;
}

.link-container {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border-radius: 40px;
    overflow: hidden;
    padding: 0 10px;
}

.copy:disabled {
    background: #f1f1f1;
    color: #333333;
    cursor: not-allowed;
}
</style>
